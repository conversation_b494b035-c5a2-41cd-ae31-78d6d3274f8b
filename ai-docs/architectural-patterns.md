# Архитектурные паттерны

## Обзор

Проект использует проверенные архитектурные паттерны для обеспечения масштабируемости, отказоустойчивости и поддерживаемости системы обработки книг.

## Слой доступа к данным

### Repository Pattern
**Класс**: `app/database/queries.py`

Централизует логику доступа к данным и обеспечивает единую точку истины для проверки состояния.

**Применение**:
- `is_source_processed()` - проверка обработанных файлов
- `check_book_duplicates()` - поиск дубликатов по хэшу
- `check_data_integrity()` - проверка целостности данных

### Object Pool Pattern
**Класс**: `app/database/connection.py`

Управляет пулом соединений PostgreSQL для эффективного использования ресурсов.

**Применение**:
```python
pool = ConnectionPool(
    conninfo=settings.DATABASE_URL,
    min_size=2,
    max_size=10,
    configure=configure_connection,
)
```

### Context Manager Pattern
**Использование**: Управление ресурсами БД

Гарантирует корректное освобождение соединений через `get_db_connection()`.

## Слой бизнес-логики

### Facade Pattern
**Класс**: `BookProcessor`

Предоставляет унифицированный интерфейс для сложного процесса обработки книг.

**Применение**:
- Инкапсулирует всю бизнес-логику в методе `process()`
- Скрывает сложность взаимодействия между компонентами
- Обеспечивает единую точку входа для обработки

### Template Method Pattern
**Класс**: `BookProcessor.process()`

Определяет скелет алгоритма обработки с фиксированными этапами:

1. Извлечение содержимого (`_extract_content`)
2. Парсинг (`_parse_to_canonical`)
3. Проверка фрагментов (`_check_fragment`)
4. Постобработка (`_postprocess_canonical_book`)
5. Проверка дубликатов (`_check_duplicates`)
6. Сохранение (`_save_and_enqueue`)

### Strategy Pattern
**Класс**: `ParserDispatcher`

ParserDispatcher использует простую условную логику (`if`/`elif`) для выбора соответствующего парсера по формату файла, что выступает функциональным аналогом паттерна «Стратегия».

**Применение**:
```python
if file_format == "fb2":
    fb2_model = self.fb2_parser.parse(file_path)
    canonical_book = self.fb2_transformer.transform(fb2_model)
```

**Расширение**: Добавление новых форматов через реализацию соответствующих парсеров.

## Слой сервисов

### Прямая инициализация зависимостей
**Классы**: `BookWorker`, `BookProcessor`

Все зависимости инициализируются напрямую внутри конструктора. Это упрощает конфигурацию (исключает сторонние DI-контейнеры), но снижает гибкость модульного тестирования – сложнее подменять реализацию мок-объектами.

**Применение в BookProcessor**:
```python
def __init__(self):
    self.file_manager = FileManager()
    self.archive_processor = ArchiveProcessor()
    self.parser_dispatcher = ParserDispatcher()
    self.hash_computer = HashComputer()
```

### Single Responsibility Principle
Каждый класс имеет одну четко определенную ответственность:

- `TaskQueueManager` - управление очередями Redis
- `FileManager` - операции с файловой системой
- `ErrorHandler` - обработка ошибок и retry-логика
- `DatabaseSaver` - сохранение в PostgreSQL

## Управление состоянием

### State Machine Pattern
**Реализация**: Изменение статуса задач и книг

Состояние теперь контролируется:
- Очередями Redis: `*_new` → `*_processing`
- Полем `books.process_status` в PostgreSQL (20, 31, 30, 40, 50…)

Физическое перемещение файлов по-прежнему выполняется `FileManager`, но **не является источником истины**: директории служат лишь для организации данных; реальное состояние определяют очереди и БД.

### Command Pattern
**Реализация**: Задачи в очередях Redis

Каждая задача представляет команду обработки книги с параметрами:
```json
{
  "source_type": 1,
  "source_id": 12345,
  "archive_path": "zip_flibusta/12345.zip",
  "book_filename": "12345.fb2",
  "archive_mtime": 1735523200.123
}
```

## Обработка ошибок

### Комбинация условной логики и Strategy Pattern
**Классы**: `ErrorHandler`, `ErrorStrategyRegistry`, `QuarantineProcessor`

ErrorHandler сначала обрабатывает известные типизированные исключения прямой условной логикой. Для всех прочих ошибок обработка делегируется стратегиям из реестра (`Strategy Pattern`).

**ErrorHandler** - основная цепочка ответственности:
1. `QuarantineError` → карантин
2. `FatalError` → критическая остановка  
3. `ProcessingError` → retry или карантин
4. Неизвестные ошибки → обработка через стратегии

**Strategy Pattern** для неизвестных ошибок:
- `CorruptedArchiveStrategy` - поврежденные архивы → карантин INVALID
- `DatabaseErrorStrategy` - ошибки БД → карантин ERROR  
- `SystemErrorStrategy` - системные ошибки → карантин ERROR
- `MemoryErrorStrategy` - критические ошибки памяти → fatal
- `UnicodeErrorStrategy` - ошибки кодировки → карантин ERROR
- `ConnectionErrorStrategy` - ошибки соединения → retry

**QuarantineProcessor** также использует Strategy Pattern:
- `ColdStorageStrategy` - для ANTHOLOGIES, FOOTNOTES* (только в аналитических инструментах), ERROR
- `EmptyMarkerStrategy` - для TRIAL, INVALID
- `PreserveStrategy` - для SMALL

*_В продакшене broken footnotes только логируются для мониторинга, карантин FOOTNOTES применяется исключительно в аналитических инструментах для изучения эвристик извлечения._

**Преимущества нового подхода**:
- Типизированная обработка ошибок вместо анализа строк
- Легкое расширение новыми типами ошибок
- Принцип открытости/закрытости (Open/Closed Principle)
- Конкретные psycopg исключения вместо широкого `except Exception`

### Pipeline Pattern
**Класс**: `TaskQueueManager`

Использует Redis pipeline для атомарных операций:
```python
pipe = self.redis_client.pipeline()
pipe.lrem(settings.QUEUE_PROCESSING, 1, raw_task)
pipe.lpush(settings.QUEUE_COMPLETED, completed_task_json)
pipe.execute()
```

## Мониторинг и восстановление

### Polling Pattern
**Класс**: `TaskMonitor`

TaskMonitor активно опрашивает состояние системы с заданным интервалом и восстанавливает зависшие задачи.

**Применение**:
- Мониторинг времени выполнения задач
- Автоматическое восстановление после сбоев
- Очистка поврежденных записей

### Recovery Pattern
**Скрипт**: `run_00_recovery.py`

Реализует комплексное восстановление системы:
- Восстановление зависших задач
- Синхронизация файлов с БД
- Проверка целостности данных

## Конфигурация и настройки

### Registry Pattern
**Модуль**: `app/settings.py`

Централизованное управление конфигурацией:
- Подключения к внешним сервисам
- Пути к директориям
- Константы очередей Redis

### Factory Method Pattern
**Применение**: Создание специализированных объектов

- `ParserDispatcher` создает парсеры по типу файла
- `get_processing_directories()` создает структуру директорий
- Connection pool создает настроенные соединения

## Масштабирование

### Worker Pattern
**Класс**: `BookWorker`

Обеспечивает горизонтальное масштабирование через множественные воркеры:
- Безопасное завершение по сигналам
- Статистика обработки
- Graceful shutdown

### Queue-Based Architecture
**Основа**: Redis очереди

Разделяет производителей и потребителей задач.

> **📋 Полная документация**: См. [doc/redis_queues.md](../doc/redis_queues.md) - единый источник истины по всем очередям Redis, API и паттернам использования.

## Принципы применения

### Когда использовать паттерны

**Repository** - для всех операций с БД, требующих консистентности

**Strategy** - при добавлении поддержки новых форматов книг

**Template Method** - для алгоритмов с фиксированной последовательностью этапов

**Прямая инициализация зависимостей:** Зависимости (например, клиенты БД, Redis, сервисы) создаются напрямую в конструкторах классов. Это упрощает конфигурацию, но усложняет мокирование при юнит-тестировании.

**State Machine** - для любых сущностей с жизненным циклом

**Pipeline** - для атомарных операций с Redis/БД

### Расширение системы

При добавлении новой функциональности следуйте существующим паттернам:
- Новые парсеры → Strategy Pattern в `ParserDispatcher`
- Новые типы ошибок → регистрация в `ErrorStrategyRegistry` или `QuarantineStrategyRegistry`
- Новые этапы обработки → Template Method в `BookProcessor`
- Новые мониторы → Polling Pattern по аналогии с `TaskMonitor`

**Пример добавления новой стратегии обработки ошибок**:
```python
class NetworkTimeoutStrategy(ErrorStrategy):
    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, (NetworkTimeout, HTTPError))
    
    def handle(self, error: Exception, task_data: dict, context: str) -> dict:
        return {"action": "retry", "reason": f"Network error: {error}"}

# Регистрация в ErrorStrategyRegistry
registry.register(NetworkTimeoutStrategy())
``` 